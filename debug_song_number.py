#!/usr/bin/env python3
"""
Debug song number extraction
"""
import re
from spy import EnhancedOC<PERSON>
from config import INPUT_DIR, PATTERNS
import os

def debug_song_number():
    """Debug song number extraction"""
    
    ocr = EnhancedOCR()
    test_file = "evang00raus_0050.jp2"
    test_path = os.path.join(INPUT_DIR, test_file)
    
    if os.path.exists(test_path):
        print(f"Processing: {test_file}")
        text = ocr.ocr_image(test_path)
        
        print(f"\nOriginal text (first 200 chars):")
        print(repr(text[:200]))
        
        print(f"\nLooking for song numbers...")
        
        # Test different patterns
        patterns = [
            PATTERNS['song_number'],      # From config
            r'(?:No\.|Nr\.|Nummer)\s*(\d+)',  # Standard pattern
            r'No\.\s*(\d+)',              # No. 46
            r'Nr\.\s*(\d+)',              # Nr. 46  
            r'Nummer\s*(\d+)',            # Nummer 46
            r'(\d{1,3})\.',               # Just number with dot
        ]
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, text, re.IGNORECASE)
            print(f"Pattern {i+1}: {pattern}")
            print(f"  Matches: {matches}")
        
        # Look for "No. 46" specifically
        print(f"\nLooking for 'No. 46' specifically:")
        if "No. 46" in text:
            print("  Found 'No. 46' in text!")
            pos = text.find("No. 46")
            print(f"  Position: {pos}")
            print(f"  Context: {repr(text[max(0, pos-20):pos+30])}")
        else:
            print("  'No. 46' not found in text")
            
        # Check if it's in the converted text
        from fraktur_converter import converter
        converted_text = converter.convert_text(text)
        print(f"\nConverted text (first 200 chars):")
        print(repr(converted_text[:200]))
        
        if "No. 46" in converted_text:
            print("  Found 'No. 46' in converted text!")
        else:
            print("  'No. 46' not found in converted text")
            
        # Test extraction manually
        number_patterns = [
            r'(?:No\.|Nr\.|Nummer)\s*(\d+)',
            r'No\.\s*(\d+)',
            r'Nr\.\s*(\d+)',
            r'Nummer\s*(\d+)',
            r'(?:^|\s)(\d{1,3})(?:\.|$)',
        ]
        
        print(f"\nTesting patterns on converted text:")
        for i, pattern in enumerate(number_patterns):
            match = re.search(pattern, converted_text, re.IGNORECASE)
            if match:
                print(f"  Pattern {i+1} matched: {match.group(1)}")
            else:
                print(f"  Pattern {i+1} no match")

if __name__ == '__main__':
    debug_song_number()
