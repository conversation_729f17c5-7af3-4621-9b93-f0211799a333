#!/usr/bin/env python3
"""
Test script to process a small batch of files
"""
import os
import shutil
from spy import SongbookProcessor
from database import db
from config import INPUT_DIR

def test_small_batch(num_files=5):
    """Test processing with a small number of files"""
    
    # Create a temporary directory with just a few files
    temp_dir = "./temp_test_files"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Get first few JP2 files
    all_files = [f for f in os.listdir(INPUT_DIR) if f.lower().endswith('.jp2')]
    all_files.sort()
    
    test_files = all_files[:num_files]
    
    print(f"Testing with {len(test_files)} files:")
    for f in test_files:
        print(f"  {f}")
        # Copy file to temp directory
        src = os.path.join(INPUT_DIR, f)
        dst = os.path.join(temp_dir, f)
        shutil.copy2(src, dst)
    
    # Temporarily change the input directory
    original_input_dir = INPUT_DIR
    
    # Update the processor to use temp directory
    processor = SongbookProcessor()
    processor.input_dir = temp_dir
    
    print("\nProcessing files...")
    processor.process_all_files()
    
    # Show results
    print("\nResults:")
    stats = db.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Show some songs if found
    songs = db.get_all_songs()
    if not songs.empty:
        print(f"\nFound {len(songs)} songs:")
        for _, song in songs.head().iterrows():
            print(f"  #{song['song_number']} - {song['title_german']} (Page {song['page_number']})")
    
    # Show index entries if found
    index_entries = db.get_index_entries()
    if not index_entries.empty:
        print(f"\nFound {len(index_entries)} index entries:")
        for _, entry in index_entries.head().iterrows():
            print(f"  {entry['title']} - Page {entry['page_number']}")
    
    # Cleanup
    shutil.rmtree(temp_dir)
    print(f"\nCleaned up temporary directory: {temp_dir}")

if __name__ == '__main__':
    test_small_batch()
