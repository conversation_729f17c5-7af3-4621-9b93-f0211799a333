#!/usr/bin/env python3
"""
Test script to process files from the middle of the book where songs are likely
"""
import os
import shutil
from spy import SongbookProcessor
from database import db
from config import INPUT_DIR

def test_middle_batch(start_index=50, num_files=10):
    """Test processing with files from the middle of the book"""
    
    # Create a temporary directory with just a few files
    temp_dir = "./temp_test_files_middle"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Get files from the middle
    all_files = [f for f in os.listdir(INPUT_DIR) if f.lower().endswith('.jp2')]
    all_files.sort()
    
    test_files = all_files[start_index:start_index + num_files]
    
    print(f"Testing with {len(test_files)} files from middle of book (starting at index {start_index}):")
    for f in test_files:
        print(f"  {f}")
        # Copy file to temp directory
        src = os.path.join(INPUT_DIR, f)
        dst = os.path.join(temp_dir, f)
        shutil.copy2(src, dst)
    
    # Update the processor to use temp directory
    processor = SongbookProcessor()
    processor.input_dir = temp_dir
    
    print("\nProcessing files...")
    processor.process_all_files()
    
    # Show results
    print("\nResults:")
    stats = db.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Show some songs if found
    songs = db.get_all_songs()
    if not songs.empty:
        print(f"\nFound {len(songs)} songs:")
        for _, song in songs.iterrows():
            print(f"  #{song['song_number']} - {song['title_german']} (Page {song['page_number']}, File: {song['filename']})")
    else:
        print("\nNo songs detected. Let's check what text was extracted...")
        
        # Check the database for processed files and show some sample text
        import sqlite3
        with sqlite3.connect('./songbook.db') as conn:
            cursor = conn.execute("""
                SELECT filename, status, error_message 
                FROM files_processed 
                ORDER BY processed_at DESC 
                LIMIT 10
            """)
            files = cursor.fetchall()
            
            print("\nRecently processed files:")
            for filename, status, error in files:
                print(f"  {filename}: {status}")
                if error:
                    print(f"    Error: {error}")
    
    # Show index entries if found
    index_entries = db.get_index_entries()
    if not index_entries.empty:
        print(f"\nFound {len(index_entries)} index entries:")
        for _, entry in index_entries.iterrows():
            print(f"  {entry['title']} - Page {entry['page_number']} (File: {entry['filename']})")
    
    # Cleanup
    shutil.rmtree(temp_dir)
    print(f"\nCleaned up temporary directory: {temp_dir}")

if __name__ == '__main__':
    test_middle_batch()
