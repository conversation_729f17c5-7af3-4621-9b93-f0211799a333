#!/usr/bin/env python3
"""
Debug raw OCR output before Fraktur conversion
"""
import re
from PIL import Image
import pytesseract
from config import INPUT_DIR, TESSERACT_CONFIG
import os

def debug_raw_ocr():
    """Debug raw OCR output"""
    
    test_file = "evang00raus_0050.jp2"
    test_path = os.path.join(INPUT_DIR, test_file)
    
    if os.path.exists(test_path):
        print(f"Processing: {test_file}")
        
        # Get raw OCR without any processing
        img = Image.open(test_path)
        config_string = f"--psm 6 --oem 3 -l {TESSERACT_CONFIG['lang']}"
        if 'config' in TESSERACT_CONFIG:
            config_string += f" {TESSERACT_CONFIG['config']}"
        
        raw_text = pytesseract.image_to_string(img, config=config_string)
        
        print(f"\nRaw OCR text (first 500 chars):")
        print(repr(raw_text[:500]))
        
        print(f"\nLooking for song numbers in raw text...")
        
        # Test different patterns
        patterns = [
            r'(?:No\.|Nr\.|Nummer)\s*(\d+)',  # Standard pattern
            r'No\.\s*(\d+)',              # No. 46
            r'Nr\.\s*(\d+)',              # Nr. 46  
            r'Nummer\s*(\d+)',            # Nummer 46
            r'(\d{1,3})\.',               # Just number with dot
        ]
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, raw_text, re.IGNORECASE)
            print(f"Pattern {i+1}: {pattern}")
            print(f"  Matches: {matches}")
        
        # Look for "No. 46" specifically
        print(f"\nLooking for 'No. 46' specifically:")
        if "No. 46" in raw_text:
            print("  Found 'No. 46' in raw text!")
            pos = raw_text.find("No. 46")
            print(f"  Position: {pos}")
            print(f"  Context: {repr(raw_text[max(0, pos-20):pos+30])}")
        else:
            print("  'No. 46' not found in raw text")
            
        # Look for any "46"
        if "46" in raw_text:
            print("  Found '46' in raw text!")
            pos = raw_text.find("46")
            print(f"  Position: {pos}")
            print(f"  Context: {repr(raw_text[max(0, pos-20):pos+30])}")
        else:
            print("  '46' not found in raw text")

if __name__ == '__main__':
    debug_raw_ocr()
