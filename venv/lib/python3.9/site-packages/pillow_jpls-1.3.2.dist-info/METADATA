Metadata-Version: 2.1
Name: pillow-jpls
Version: 1.3.2
Summary: A JPEG-LS plugin for the Pillow imaging library
Author-Email: <PERSON> <<EMAIL>>
License: BSD-3-Clause
Project-URL: Github, https://github.com/planetmarshall/pillow-jpls
Requires-Python: >=3.7
Requires-Dist: Pillow
Description-Content-Type: text/markdown
License-File: LICENSE.md

JPEG-LS plugin for Python Pillow
================================

![Build status](https://github.com/planetmarshall/pillow-jpls/actions/workflows/build_deploy.yml/badge.svg)


A plugin for the Python [Pillow](https://pillow.readthedocs.io/en/stable/) imaging library for the
JPEG-LS codec,
based on the [Charls](https://github.com/team-charls/charls) JPEG-LS implemetation. 
Python bindings implemented using [pybind11](https://pybind11.readthedocs.io/en/stable/).

Available on the [Python Package Index](https://pypi.org/project/pillow-jpls/)

Usage
-----

```
pip install pillow-jpls
```

### With Pillow
```.py
import pillow_jpls
from PIL import Image
from io import BytesIO

img = Image.open("image.jls")
img.save("image_copy.jls)

# Saving to a buffer
buffer = BytesIO()
img.save(buffer, "JPEG-LS")
```

### Options

The encoder supports the following options. See the specification for details, and the tests for 
example usage.

* `near_lossless` : `0` is lossless - the default.
* `interleave` : one of `none`, `line` or `sample` 
* `bits_per_component` : override the number of bits encoded per component, otherwise taken from the image format
* `maxval`: override the maximum value of any component, otherwise taken from `bits_per_component`
* `t1`
* `t2`
* `t3`
* `reset`
* `spiff`: Use a SPIFF image header, generated by default. Use `None` to just use a standard JPEG header.

Build
-----

The build is driven by [Scikit Build Core](https://github.com/scikit-build/scikit-build-core).
[cibuildwheel](https://github.com/joerick/cibuildwheel) is used to generate packages using Github Actions, and
C++ dependencies are satisfied by [conan](https://docs.conan.io/en/latest/) using [cmake-conan](https://github.com/conan-io/cmake-conan).

```
pip install build
python -m build
```

### Build without conan

To use system dependencies instead of using conan:

```
python -m build -C cmake.args="--preset sysdeps"
pip install dist/*.whl
```

Note that wheels created this way are unlikely to be portable and are suitable only for local use. A Dockerfile is provided
in the root of the repository to install the prerequisites, see also the CI job that builds a wheel using only system
dependencies.

Tests
-----

A suite of tests covering the applicable conformance tests from the specification is provided.

```
pip install -r requirements.txt
pytest -v test
```

Limitations
-----------

16 <USER> <GROUP> images are not supported, as these are not supported in Pillow.
16bit greyscale images are supported, however.


References
----------

* [JPEG-LS on Wikipedia](https://en.wikipedia.org/wiki/Lossless_JPEG#JPEG-LS)
* [The ITU T.87 Specification](https://www.itu.int/rec/T-REC-T.87-199806-I/en)
