"""
Fraktur and old German text normalization module
"""
import re
import unicodedata
from typing import Dict, List
from config import FRAKTUR_MAPPINGS

class FrakturConverter:
    """Converts old German Fraktur text to modern German"""
    
    def __init__(self):
        self.mappings = FRAKTUR_MAPPINGS.copy()
        self.old_german_patterns = self._load_old_german_patterns()
    
    def _load_old_german_patterns(self) -> Dict[str, str]:
        """Load patterns for old German spelling to modern German"""
        return {
            # Old spelling patterns
            r'\bth\b': 't',  # th -> t
            r'ck': 'k',      # ck -> k in some old texts
            r'ph': 'f',      # ph -> f
            r'(?<=\w)y(?=\w)': 'i',  # y -> i in middle of words
            
            # Common old German words
            r'\bvnd\b': 'und',
            r'\bvnser\b': 'unser',
            r'\bvns\b': 'uns',
            r'\bvber\b': 'über',
            r'\bvom\b': 'vom',
            r'\bvon\b': 'von',
            r'\bviel\b': 'viel',
            
            # Double consonants
            r'(?<=\w)ff(?=\w)': 'f',
            r'(?<=\w)ss(?=\w)': 's',
            r'(?<=\w)tt(?=\w)': 't',
            
            # Capitalization patterns (old German capitalized more nouns)
            # This will be handled separately
        }
    
    def normalize_fraktur_chars(self, text: str) -> str:
        """Replace Fraktur-specific characters with modern equivalents"""
        for old_char, new_char in self.mappings.items():
            text = text.replace(old_char, new_char)
        return text
    
    def normalize_old_german_spelling(self, text: str) -> str:
        """Convert old German spelling patterns to modern German"""
        for pattern, replacement in self.old_german_patterns.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        return text
    
    def clean_ocr_artifacts(self, text: str) -> str:
        """Remove common OCR artifacts and errors"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Fix common OCR character confusions in Fraktur
        ocr_fixes = {
            # Number/letter confusions
            r'(?<!\w)1(?=\w)': 'l',  # 1 -> l at word start
            r'(?<!\w)0(?=\w)': 'o',  # 0 -> o at word start
            r'(?<=\w)1(?!\w)': 'l',  # 1 -> l at word end
            r'(?<=\w)0(?!\w)': 'o',  # 0 -> o at word end

            # Common Fraktur OCR errors
            r'rn': 'm',              # rn -> m (common OCR error)
            r'cl': 'd',              # cl -> d (common in Fraktur)
            r'vv': 'w',              # vv -> w
            r'ii': 'ü',              # ii -> ü sometimes
            r'oe': 'ö',              # oe -> ö
            r'ae': 'ä',              # ae -> ä
            r'ue': 'ü',              # ue -> ü

            # Specific Fraktur character fixes
            r'Cin\b': 'Ein',         # Common OCR error for "Ein"
            r'Gwadlen': 'Gnaden',    # "Gnadenruf" OCR error
            r'Gnadenvwl': 'Gnadenruf',  # "Gnadenruf" OCR error
            r'evtiwt': 'ertönt',     # "ertönt" OCR error
            r'wicht': 'nicht',       # "nicht" OCR error
            r'Werfukauny': 'Versuchung',  # "Versuchung" OCR error
            r'Goly': 'Folg',         # "Folg" OCR error
            r'fomm': 'komm',         # "komm" OCR error
            r'fcjulo': 'schuld',     # "schuld" OCR error
            r'Gnaebenzcuf': 'Gnadenruf',  # "Gnadenruf" OCR error
            r'Gnäbenzcuf': 'Gnadenruf',  # "Gnadenruf" OCR error
            r'tont': 'tönt',         # "tönt" OCR error

            # Remove obvious OCR noise patterns (but preserve song numbers)
            r'[ZA\)\>\s\<\.]+(?=\s[A-Z])': '',  # Remove noise before titles (but not digits)
            r'\s+[a-z]{1,2}\s+': ' ',  # Remove single/double letter artifacts
        }

        for pattern, replacement in ocr_fixes.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

        return text.strip()
    
    def normalize_punctuation(self, text: str) -> str:
        """Normalize punctuation marks"""
        # Replace various dash types with standard dash
        text = re.sub(r'[–—―]', '-', text)
        
        # Replace various quote types
        text = re.sub(r'[""„"]', '"', text)
        text = re.sub(r'[''`´]', "'", text)
        
        # Fix spacing around punctuation
        text = re.sub(r'\s+([,.;:!?])', r'\1', text)
        text = re.sub(r'([,.;:!?])\s*', r'\1 ', text)
        
        return text
    
    def convert_text(self, text: str) -> str:
        """Main conversion function - applies all normalizations"""
        if not text:
            return text
        
        # Step 1: Normalize Unicode
        text = unicodedata.normalize('NFKC', text)
        
        # Step 2: Replace Fraktur characters
        text = self.normalize_fraktur_chars(text)
        
        # Step 3: Clean OCR artifacts
        text = self.clean_ocr_artifacts(text)
        
        # Step 4: Normalize old German spelling
        text = self.normalize_old_german_spelling(text)
        
        # Step 5: Normalize punctuation
        text = self.normalize_punctuation(text)
        
        return text
    
    def extract_modern_title(self, title: str) -> str:
        """Extract and modernize a song title"""
        if not title:
            return title
        
        # Convert the title
        modern_title = self.convert_text(title)
        
        # Additional title-specific cleaning
        # Remove leading numbers or "No." patterns
        modern_title = re.sub(r'^(?:No\.|Nr\.|Nummer)\s*\d+\.?\s*', '', modern_title)
        
        # Capitalize properly (German title case)
        words = modern_title.split()
        if words:
            # First word always capitalized
            words[0] = words[0].capitalize()
            # Capitalize nouns (basic heuristic - words longer than 3 chars)
            for i in range(1, len(words)):
                if len(words[i]) > 3 and words[i].lower() not in ['und', 'oder', 'der', 'die', 'das', 'ein', 'eine']:
                    words[i] = words[i].capitalize()
        
        return ' '.join(words).strip()

# Global converter instance
converter = FrakturConverter()
