#!/usr/bin/env python3
"""
Script to find the table of contents (Inhaltsverzeichnis) in the songbook
"""
import os
import re
from spy import <PERSON>hancedOC<PERSON>
from database import db
from config import INPUT_DIR

def find_index_pages():
    """Find pages that contain the table of contents"""
    
    ocr = EnhancedOCR()
    
    # Get all JP2 files
    all_files = [f for f in os.listdir(INPUT_DIR) if f.lower().endswith('.jp2')]
    all_files.sort()
    
    print(f"Searching for index pages in {len(all_files)} files...")
    
    # The index is usually at the end of the book, so start from the end
    # Check last 50 files
    files_to_check = all_files[-50:]
    
    index_files = []
    
    for filename in files_to_check:
        filepath = os.path.join(INPUT_DIR, filename)
        print(f"Checking {filename}...")
        
        try:
            text = ocr.ocr_image(filepath)
            
            # Check if this is an index page
            if ocr.is_index_page(text):
                print(f"  ✓ INDEX PAGE FOUND: {filename}")
                index_files.append(filename)
                
                # Extract and show some index entries
                entries = ocr.extract_index_entries(text, filename)
                print(f"    Found {len(entries)} index entries:")
                for entry in entries[:5]:  # Show first 5
                    print(f"      {entry['title']} - Page {entry['page_number']}")
                if len(entries) > 5:
                    print(f"      ... and {len(entries) - 5} more")
                
                # Save to database
                for entry in entries:
                    db.add_index_entry(
                        entry['title'], 
                        entry['page_number'], 
                        filename
                    )
                
                print()
            else:
                # Show a sample of the text to see what we're getting
                sample_text = text[:200].replace('\n', ' ')
                print(f"    Sample text: {sample_text}...")
                
        except Exception as e:
            print(f"  Error processing {filename}: {e}")
    
    if index_files:
        print(f"\nFound {len(index_files)} index pages:")
        for f in index_files:
            print(f"  {f}")
        
        # Show statistics
        stats = db.get_statistics()
        print(f"\nIndex entries in database: {stats['total_index_entries']}")
        
        # Show some index entries
        index_entries = db.get_index_entries()
        if not index_entries.empty:
            print(f"\nSample index entries:")
            for _, entry in index_entries.head(10).iterrows():
                print(f"  {entry['title']} - Page {entry['page_number']}")
    else:
        print("\nNo index pages found in the last 50 files.")
        print("The index might be at the beginning or middle of the book.")
        
        # Try checking the first 20 files
        print("\nChecking first 20 files...")
        files_to_check = all_files[:20]
        
        for filename in files_to_check:
            filepath = os.path.join(INPUT_DIR, filename)
            print(f"Checking {filename}...")
            
            try:
                text = ocr.ocr_image(filepath)
                
                if ocr.is_index_page(text):
                    print(f"  ✓ INDEX PAGE FOUND: {filename}")
                    index_files.append(filename)
                    
                    # Extract and save entries
                    entries = ocr.extract_index_entries(text, filename)
                    print(f"    Found {len(entries)} index entries")
                    
                    for entry in entries:
                        db.add_index_entry(
                            entry['title'], 
                            entry['page_number'], 
                            filename
                        )
                else:
                    sample_text = text[:100].replace('\n', ' ')
                    print(f"    Sample: {sample_text}...")
                    
            except Exception as e:
                print(f"  Error: {e}")

if __name__ == '__main__':
    find_index_pages()
