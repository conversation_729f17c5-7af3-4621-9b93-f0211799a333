"""
Configuration settings for the OCR Songbook project
"""
import os

# Paths
INPUT_DIR = "/Users/<USER>/Downloads/_Archive/EvangeliumsLieder Buch/"
OUTPUT_DIR = "./output"
DATABASE_PATH = "./songbook.db"
LOG_FILE = "./ocr_log.txt"

# OCR Settings
TESSERACT_CONFIG = {
    'lang': 'deu+eng+frk',  # German, English, and Fraktur
    'config': '--psm 6 --oem 3'
}

# Image preprocessing settings
IMAGE_PREPROCESSING = {
    'resize_factor': 2.0,  # Scale up for better OCR
    'denoise': True,
    'enhance_contrast': True,
    'binarize': True
}

# Text patterns for detection
PATTERNS = {
    'song_number': r'(?:No\.|Nr\.|Nummer)\s*(\d+)',
    'index_title': r'(?:Inhaltsverzeichnis|Verzeichnis|Register|Index)',
    'song_indicators': [
        r'Strophe',
        r'Vers',
        r'<PERSON><PERSON>',
        r'Refrain',
        r'Mel<PERSON>ie',
        r'\d+\.\s*[A-ZÄÖÜ]',  # Numbered verses
    ],
    'page_number': r'(?:Seite|S\.)\s*(\d+)|^(\d+)$'
}

# Fraktur to modern German mappings
FRAKTUR_MAPPINGS = {
    'ſ': 's',  # Long s
    'ß': 'ss',  # Sometimes needs conversion
    'ä': 'ae',  # Alternative representation
    'ö': 'oe',
    'ü': 'ue',
    'Ä': 'Ae',
    'Ö': 'Oe',
    'Ü': 'Ue',
    # Add more mappings as needed
}

# Database schema
DB_SCHEMA = {
    'songs': '''
        CREATE TABLE IF NOT EXISTS songs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            song_number TEXT,
            title_german TEXT,
            title_original TEXT,
            title_english TEXT,
            page_number INTEGER,
            filename TEXT,
            full_text TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''',
    'index_entries': '''
        CREATE TABLE IF NOT EXISTS index_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            page_number INTEGER,
            filename TEXT,
            song_id INTEGER,
            FOREIGN KEY (song_id) REFERENCES songs (id)
        )
    ''',
    'files_processed': '''
        CREATE TABLE IF NOT EXISTS files_processed (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT UNIQUE,
            file_path TEXT,
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT,
            error_message TEXT
        )
    '''
}

# Ensure output directory exists
os.makedirs(OUTPUT_DIR, exist_ok=True)
