#!/usr/bin/env python3
"""
Debug script to see what text is being extracted
"""
import sqlite3
from spy import <PERSON>han<PERSON><PERSON><PERSON>
from config import INPUT_DIR
import os

def debug_extraction():
    """Debug what text is being extracted from files"""
    
    # Check what's in the database
    with sqlite3.connect('./songbook.db') as conn:
        cursor = conn.execute("""
            SELECT song_number, title_german, title_original, filename, full_text 
            FROM songs 
            ORDER BY CAST(song_number AS INTEGER)
            LIMIT 3
        """)
        songs = cursor.fetchall()
        
        print("Sample songs from database:")
        print("=" * 80)
        
        for song_number, title_german, title_original, filename, full_text in songs:
            print(f"\nSong #{song_number} from {filename}")
            print(f"German Title: '{title_german}'")
            print(f"Original Title: '{title_original}'")
            print("\nFull OCR Text (first 500 chars):")
            print("-" * 40)
            print(full_text[:500] if full_text else "No text")
            print("-" * 40)
    
    # Test OCR on a specific file
    print("\n" + "=" * 80)
    print("Testing OCR on a specific file...")
    
    ocr = EnhancedOCR()
    test_file = "evang00raus_0050.jp2"
    test_path = os.path.join(INPUT_DIR, test_file)
    
    if os.path.exists(test_path):
        print(f"\nProcessing: {test_file}")
        text = ocr.ocr_image(test_path)
        
        print(f"\nExtracted text ({len(text)} characters):")
        print("-" * 40)
        print(text[:1000])  # First 1000 characters
        print("-" * 40)
        
        # Test song info extraction
        song_info = ocr.extract_song_info(text, test_file)
        print(f"\nExtracted song info:")
        for key, value in song_info.items():
            if key != 'full_text':  # Skip full text for readability
                print(f"  {key}: '{value}'")
    else:
        print(f"File not found: {test_path}")

if __name__ == '__main__':
    debug_extraction()
