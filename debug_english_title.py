#!/usr/bin/env python3
"""
Debug English title extraction
"""
import re
from spy import EnhancedOC<PERSON>
from config import INPUT_DIR
import os

def debug_english_title():
    """Debug English title extraction"""
    
    ocr = EnhancedOCR()
    test_file = "evang00raus_0050.jp2"
    test_path = os.path.join(INPUT_DIR, test_file)
    
    if os.path.exists(test_path):
        print(f"Processing: {test_file}")
        text = ocr.ocr_image(test_path)
        
        print(f"\nLooking for English titles...")
        
        # Split text into lines
        lines = text.replace('|', '\n').split('\n')
        
        print(f"\nAll lines:")
        for i, line in enumerate(lines[:15]):  # First 15 lines
            print(f"{i:2}: {repr(line)}")
        
        print(f"\nLooking for 'HEAR THY WELCOME VOICE':")
        if "HEAR THY WELCOME VOICE" in text:
            print("  Found in text!")
        else:
            print("  Not found in text")
            
        if "HeEaR THY WELCOME VoICcE" in text:
            print("  Found OCR version in text!")
        else:
            print("  OCR version not found")
        
        # Test English title patterns
        english_candidates = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            print(f"\nTesting line {i}: {repr(line)}")
            
            # Clean the line first
            clean_english_line = re.sub(r'^[{\|\s]+', '', line)
            clean_english_line = re.sub(r'[{\|\s]+$', '', clean_english_line)
            
            print(f"  Cleaned: {repr(clean_english_line)}")
            
            if re.match(r'^[A-Z][A-Z\s\-\'\.]+$', clean_english_line):
                print(f"  Matches uppercase pattern")
                if len(clean_english_line) > 8:
                    print(f"  Length > 8")
                    if not re.search(r'\b(?:G\.H\.C|ANON|PALMER|HARTSOUGH)\b', clean_english_line):
                        print(f"  Not composer name")
                        english_candidates.append(clean_english_line.strip())
                        print(f"  ADDED TO CANDIDATES!")
                    else:
                        print(f"  Filtered out as composer name")
                else:
                    print(f"  Too short")
            else:
                print(f"  Doesn't match uppercase pattern")
        
        print(f"\nEnglish candidates found: {english_candidates}")

if __name__ == '__main__':
    debug_english_title()
