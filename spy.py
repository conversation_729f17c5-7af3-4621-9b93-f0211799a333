import os
import re
import pandas as pd
from PIL import Image
import pytesseract

# Ordner mit den jp2-Dateien
input_dir = 'pfad/zum/liederbuch'  # <--- ANPASSEN!

def ocr_image(image_path):
    """OCR für ein Bild."""
    img = Image.open(image_path)
    text = pytesseract.image_to_string(img, lang='deu+eng')
    return text

def is_index_page(text):
    """Erkennt Inhaltsverzeichnis-Seiten."""
    return 'Inhaltsverzeichnis' in text or 'Verzeichnis' in text

def is_song_page(text):
    """Erkennt Liedseiten anhand typischer Muster."""
    # Beispiel: 'No. 123', 'Strophe', 'Chor', Notenzeilen (z.B. viele | oder Taktstriche)
    return bool(re.search(r'No\.\s*\d+', text)) and (
        'Strophe' in text or 'Chor' in text or len(re.findall(r'\|', text)) > 5
    )

def extract_song_titles(text):
    """Extrahiert Liednummer, deutschen und englischen Titel."""
    m_nr = re.search(r'No\.\s*(\d+)', text)
    m_de = re.search(r'No\.\s*\d+\.\s*([^\n]+)', text)
    # Englischer Titel ist oft in Großbuchstaben unter dem deutschen Titel
    m_en = re.search(r'\n([A-Z][A-Z\s\-]+)\.', text)
    nr = m_nr.group(1).strip() if m_nr else ''
    title_de = m_de.group(1).strip() if m_de else ''
    title_en = m_en.group(1).strip() if m_en else ''
    return nr, title_de, title_en

def extract_index_from_content(text):
    """Extrahiert das Inhaltsverzeichnis als Liste von Dicts."""
    index = []
    for line in text.split('\n'):
        m = re.match(r'(.+?)\s+(\d+)$', line.strip())
        if m:
            title, page = m.groups()
            index.append({'de_title': title.strip(), 'page': page.strip()})
    return index

def main():
    results = []
    index_data = []
    index_found = False

    for filename in sorted(os.listdir(input_dir)):
        if not filename.lower().endswith('.jp2'):
            continue
        filepath = os.path.join(input_dir, filename)
        print(f"OCR für: {filename}")
        try:
            text = ocr_image(filepath)
        except Exception as e:
            print(f"Fehler bei {filename}: {e}")
            continue

        # Prüfe auf Inhaltsverzeichnis
        if is_index_page(text):
            if not index_found:
                print(f"Inhaltsverzeichnis gefunden in {filename}")
                index_data.extend(extract_index_from_content(text))
                index_found = True
            continue  # Inhaltsverzeichnis nicht als Lied zählen

        # Prüfe auf Liedseite
        if is_song_page(text):
            nr, title_de, title_en = extract_song_titles(text)
            results.append({
                'nr': nr,
                'de_title': title_de,
                'en_title': title_en,
                'file': filename
            })

    # Inhaltsverzeichnis als DataFrame
    index_df = pd.DataFrame(index_data)
    # Lieddaten als DataFrame
    songs_df = pd.DataFrame(results)

    # Verknüpfen: Nach Nummer oder Titel abgleichen und ergänzen
    if not index_df.empty:
        final_df = pd.merge(songs_df, index_df, left_on='nr', right_on='page', how='left', suffixes=('', '_idx'))
    else:
        final_df = songs_df

    # Speichern als CSV
    final_df.to_csv('lieder_index.csv', index=False)
    print("Index gespeichert als lieder_index.csv")

if __name__ == '__main__':
    main()
