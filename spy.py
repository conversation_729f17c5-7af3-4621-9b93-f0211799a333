"""
Enhanced OCR system for German songbook digitization
Supports JP2 files, Fraktur text, and creates searchable database
"""
import os
import re
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
import pandas as pd
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
from tqdm import tqdm

# Import our custom modules
from config import INPUT_DIR, OUTPUT_DIR, TESSERACT_CONFIG, IMAGE_PREPROCESSING, PATTERNS
from fraktur_converter import converter
from database import db

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ocr_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedOCR:
    """Enhanced OCR processor for old German songbooks"""

    def __init__(self):
        self.tesseract_config = TESSERACT_CONFIG
        self.preprocessing_config = IMAGE_PREPROCESSING
        self.patterns = PATTERNS

    def preprocess_image(self, image_path: str) -> Image.Image:
        """Preprocess JP2 image for better OCR results"""
        try:
            # Open JP2 file
            img = Image.open(image_path)

            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # Resize for better OCR (scale up)
            if self.preprocessing_config['resize_factor'] != 1.0:
                new_size = (
                    int(img.width * self.preprocessing_config['resize_factor']),
                    int(img.height * self.preprocessing_config['resize_factor'])
                )
                img = img.resize(new_size, Image.Resampling.LANCZOS)

            # Enhance contrast
            if self.preprocessing_config['enhance_contrast']:
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.5)

            # Denoise
            if self.preprocessing_config['denoise']:
                img = img.filter(ImageFilter.MedianFilter(size=3))

            # Convert to grayscale for binarization
            img_gray = img.convert('L')

            # Binarization using adaptive threshold
            if self.preprocessing_config['binarize']:
                img_array = np.array(img_gray)
                img_binary = cv2.adaptiveThreshold(
                    img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY, 11, 2
                )
                img = Image.fromarray(img_binary)
            else:
                img = img_gray

            return img

        except Exception as e:
            logger.error(f"Error preprocessing image {image_path}: {e}")
            # Fallback to original image
            return Image.open(image_path)

    def ocr_image(self, image_path: str) -> str:
        """Perform OCR on image with preprocessing"""
        try:
            # Preprocess image
            img = self.preprocess_image(image_path)

            # Perform OCR with Fraktur support
            config_string = f"--psm 6 --oem 3 -l {self.tesseract_config['lang']}"
            if 'config' in self.tesseract_config:
                config_string += f" {self.tesseract_config['config']}"

            text = pytesseract.image_to_string(img, config=config_string)

            # Convert Fraktur text to modern German
            text = converter.convert_text(text)

            return text

        except Exception as e:
            logger.error(f"OCR failed for {image_path}: {e}")
            return ""

    def is_index_page(self, text: str) -> bool:
        """Detect table of contents pages"""
        index_patterns = self.patterns['index_title']
        return bool(re.search(index_patterns, text, re.IGNORECASE))

    def is_song_page(self, text: str) -> bool:
        """Detect song pages based on patterns"""
        # Check for song number
        song_number_match = re.search(self.patterns['song_number'], text, re.IGNORECASE)
        if not song_number_match:
            return False

        # Check for song indicators
        indicator_count = 0
        for pattern in self.patterns['song_indicators']:
            if re.search(pattern, text, re.IGNORECASE):
                indicator_count += 1

        # Also check for musical notation indicators (many vertical bars)
        bar_count = len(re.findall(r'\|', text))

        return indicator_count >= 1 or bar_count > 5

    def extract_song_info(self, text: str, filename: str) -> Dict:
        """Extract comprehensive song information"""
        song_info = {
            'song_number': '',
            'title_german': '',
            'title_original': '',
            'title_english': '',
            'page_number': None,
            'filename': filename,
            'full_text': text
        }

        # Extract song number - try multiple patterns
        number_patterns = [
            self.patterns['song_number'],  # Standard pattern
            r'No\.\s*(\d+)',              # No. 46
            r'Nr\.\s*(\d+)',              # Nr. 46
            r'Nummer\s*(\d+)',            # Nummer 46
            r'(?:^|\s)(\d{1,3})(?:\.|$)',  # Just number at start/end
        ]

        for pattern in number_patterns:
            number_match = re.search(pattern, text, re.IGNORECASE)
            if number_match:
                song_info['song_number'] = number_match.group(1)
                break

        # Extract page number from filename or text
        page_match = re.search(r'(\d+)', filename)
        if page_match:
            song_info['page_number'] = int(page_match.group(1))

        # Extract titles - improved logic for old German texts
        # Look for titles near the song number
        lines = text.replace('|', '\n').split('\n')  # Split on | as well as newlines
        title_candidates = []
        english_candidates = []

        # Find the line with the song number
        song_number_line_idx = -1
        for i, line in enumerate(lines):
            if re.search(self.patterns['song_number'], line, re.IGNORECASE):
                song_number_line_idx = i
                break

        # Look for titles around the song number line
        search_range = range(max(0, song_number_line_idx - 2),
                           min(len(lines), song_number_line_idx + 5))

        for i in search_range:
            line = lines[i].strip()
            if not line or len(line) < 4:
                continue

            # Skip lines that are just numbers, musical notation, or very short
            if line.isdigit() or len(line) < 4:
                continue

            # Skip lines with only musical symbols
            if re.match(r'^[|\-=\s\d\.]+$', line):
                continue

            # Look for English titles (usually in ALL CAPS, but OCR may have mixed case)
            # Clean the line first
            clean_english_line = re.sub(r'^[{\|\s]+', '', line)  # Remove leading symbols
            clean_english_line = re.sub(r'[{\|\s]+$', '', clean_english_line)  # Remove trailing symbols
            clean_english_line = re.sub(r'\s+H\s+C,.*$', '', clean_english_line)  # Remove composer info
            clean_english_line = re.sub(r'\s+-No\.\s*\d+.*$', '', clean_english_line)  # Remove song numbers

            # Check if it looks like an English title (mostly uppercase letters)
            if len(clean_english_line) > 8:
                uppercase_ratio = sum(1 for c in clean_english_line if c.isupper()) / len(clean_english_line.replace(' ', ''))
                if uppercase_ratio > 0.6:  # At least 60% uppercase
                    # Filter out lines that are mostly composer names or other metadata
                    if not re.search(r'\b(?:G\.H\.C|ANON|PALMER|HARTSOUGH|HARTSOUEN)\b', clean_english_line, re.IGNORECASE):
                        english_candidates.append(clean_english_line.strip())
                continue

            # Look for German titles (mixed case, may have OCR errors)
            # Remove song number from the line first
            clean_line = re.sub(self.patterns['song_number'], '', line, flags=re.IGNORECASE).strip()
            clean_line = re.sub(r'^[.\-\s\|\{\}]+', '', clean_line)  # Remove leading punctuation
            clean_line = re.sub(r'[.\-\s\|\{\}]+$', '', clean_line)  # Remove trailing punctuation

            # Remove obvious OCR noise patterns
            clean_line = re.sub(r'^[ZA\)\>\s\<\d\.\,\-]+', '', clean_line)  # Remove noise at start
            clean_line = re.sub(r'\s+[a-z]{1,2}\s+', ' ', clean_line)  # Remove single letter artifacts
            clean_line = re.sub(r'^[goo:\s\<\>\,\-]+', '', clean_line)  # Remove "goo:" type noise
            clean_line = clean_line.strip()

            if len(clean_line) > 8 and not clean_line.isdigit():
                # Check if it looks like a title (starts with capital letter or has mixed case)
                # Also filter out lines with too many special characters (likely OCR noise)
                special_char_ratio = len(re.findall(r'[^\w\s\-äöüÄÖÜß]', clean_line)) / len(clean_line)

                if (re.match(r'^[A-ZÄÖÜ]', clean_line) or any(c.islower() for c in clean_line)) and special_char_ratio < 0.3:
                    title_candidates.append(clean_line)

        # Process title candidates
        if title_candidates:
            # Take the first reasonable German title candidate
            best_title = title_candidates[0]
            song_info['title_original'] = best_title
            song_info['title_german'] = converter.extract_modern_title(best_title)
        elif song_info['song_number']:
            # If no title candidates but we have a song number, try to extract from the song number line
            for line in lines:
                if song_info['song_number'] in line:
                    # Extract title from the same line as song number
                    title_part = re.sub(r'(?:No\.|Nr\.|Nummer)\s*\d+\.?\s*', '', line, flags=re.IGNORECASE)
                    title_part = re.sub(r'^[ZA\)\>\s\<\d\.\,\-goo:]+', '', title_part)
                    title_part = title_part.strip()
                    if len(title_part) > 5:
                        song_info['title_original'] = title_part
                        song_info['title_german'] = converter.extract_modern_title(title_part)
                    break

        # Process English title candidates
        if english_candidates:
            # Take the first English title
            song_info['title_english'] = english_candidates[0].title()

        return song_info

    def extract_index_entries(self, text: str, filename: str) -> List[Dict]:
        """Extract index/table of contents entries"""
        entries = []
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if not line or len(line) < 5:
                continue

            # Look for pattern: Title ... Page Number
            # Various patterns for page references
            patterns = [
                r'(.+?)\s+\.{2,}\s*(\d+)$',  # Title ... 123
                r'(.+?)\s+(\d+)$',           # Title 123
                r'(.+?)\s+S\.?\s*(\d+)$',    # Title S. 123
            ]

            for pattern in patterns:
                match = re.match(pattern, line)
                if match:
                    title, page = match.groups()
                    title = title.strip(' .')

                    if len(title) > 3:  # Valid title length
                        entries.append({
                            'title': converter.extract_modern_title(title),
                            'original_title': title,
                            'page_number': int(page),
                            'filename': filename
                        })
                    break

        return entries

class SongbookProcessor:
    """Main processor for the songbook OCR project"""

    def __init__(self):
        self.ocr = EnhancedOCR()
        self.input_dir = INPUT_DIR
        self.processed_count = 0
        self.error_count = 0

    def process_all_files(self):
        """Process all JP2 files in the input directory"""
        logger.info(f"Starting OCR processing of files in {self.input_dir}")

        # Get all JP2 files
        jp2_files = [f for f in os.listdir(self.input_dir) if f.lower().endswith('.jp2')]
        jp2_files.sort()

        logger.info(f"Found {len(jp2_files)} JP2 files to process")

        # Process files with progress bar
        for filename in tqdm(jp2_files, desc="Processing JP2 files"):
            if db.is_file_processed(filename):
                logger.info(f"Skipping already processed file: {filename}")
                continue

            self.process_single_file(filename)

        # Link index entries to songs
        logger.info("Linking index entries to songs...")
        db.link_index_to_songs()

        # Generate reports
        self.generate_reports()

        logger.info(f"Processing complete. Processed: {self.processed_count}, Errors: {self.error_count}")

    def process_single_file(self, filename: str):
        """Process a single JP2 file"""
        filepath = os.path.join(self.input_dir, filename)

        try:
            logger.debug(f"Processing: {filename}")

            # Perform OCR
            text = self.ocr.ocr_image(filepath)

            if not text.strip():
                logger.warning(f"No text extracted from {filename}")
                db.mark_file_processed(filename, filepath, 'warning', 'No text extracted')
                return

            # Check if it's an index page
            if self.ocr.is_index_page(text):
                logger.info(f"Index page found: {filename}")
                entries = self.ocr.extract_index_entries(text, filename)

                for entry in entries:
                    db.add_index_entry(
                        entry['title'],
                        entry['page_number'],
                        filename
                    )

                db.mark_file_processed(filename, filepath, 'success')
                self.processed_count += 1
                return

            # Check if it's a song page
            if self.ocr.is_song_page(text):
                logger.info(f"Song page found: {filename}")
                song_info = self.ocr.extract_song_info(text, filename)

                db.add_song(song_info)
                db.mark_file_processed(filename, filepath, 'success')
                self.processed_count += 1
                return

            # Neither index nor song page
            logger.debug(f"No recognizable content in {filename}")
            db.mark_file_processed(filename, filepath, 'success', 'No recognizable content')
            self.processed_count += 1

        except Exception as e:
            logger.error(f"Error processing {filename}: {e}")
            db.mark_file_processed(filename, filepath, 'error', str(e))
            self.error_count += 1

    def generate_reports(self):
        """Generate output reports"""
        logger.info("Generating reports...")

        # Export to Excel
        excel_path = os.path.join(OUTPUT_DIR, 'songbook_complete.xlsx')
        db.export_to_excel(excel_path)
        logger.info(f"Excel report saved to: {excel_path}")

        # Export songs to CSV for compatibility
        songs_df = db.get_all_songs()
        csv_path = os.path.join(OUTPUT_DIR, 'songs.csv')
        songs_df.to_csv(csv_path, index=False)
        logger.info(f"Songs CSV saved to: {csv_path}")

        # Print statistics
        stats = db.get_statistics()
        logger.info("Processing Statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")

def main():
    """Main entry point"""
    processor = SongbookProcessor()
    processor.process_all_files()

if __name__ == '__main__':
    main()
